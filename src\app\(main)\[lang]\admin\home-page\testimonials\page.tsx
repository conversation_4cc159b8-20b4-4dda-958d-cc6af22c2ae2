"use client";

import { useState, useEffect } from 'react';
import { FiSave, FiEdit3, FiX, FiPlus, FiTrash2, FiStar, FiEye, FiSettings, FiUsers, FiMessageSquare, FiImage } from 'react-icons/fi';
import { useAuth } from '@/hooks/useAuth';
import { authenticatedApiCall } from '@/utils/api';
import { useToast } from '@/contexts/ToastContext';
import { createToast } from '@/utils/toast';
import Testimonials from '@/components/home/<USER>';

interface TestimonialData {
  id: number;
  name: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  content: {
    en: string;
    ar: string;
  };
  initials: {
    en: string;
    ar: string;
  };
  rating: number;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
  updated_by?: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
}

interface SectionSettings {
  id?: number;
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  subtitle: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
  updated_by?: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
}

export default function HomePageTestimonialsManagement() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { showToast } = useToast();

  const [sectionSettings, setSectionSettings] = useState<SectionSettings>({
    badge: {
      en: "Client Success Stories",
      ar: "قصص نجاح العملاء"
    },
    title: {
      en: "Client Testimonials",
      ar: "شهادات العملاء"
    },
    subtitle: {
      en: "Hear what our satisfied clients say about their experience with Mazaya Capital",
      ar: "اسمع ما يقوله عملاؤنا الراضون عن تجربتهم مع مزايا كابيتال"
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [originalSectionSettings, setOriginalSectionSettings] = useState<SectionSettings | null>(null);
  const [originalTestimonials, setOriginalTestimonials] = useState<TestimonialData[]>([]);

  const [testimonials, setTestimonials] = useState<TestimonialData[]>([]);

  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [editingTestimonial, setEditingTestimonial] = useState<string | null>(null);

  // Fetch testimonials data when authentication is ready
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      fetchTestimonialsData();
    } else if (!authLoading && !isAuthenticated) {
      setIsLoading(false);
      showToast(createToast.error(
        'Authentication required',
        'Please log in to access this page'
      ));
    }
  }, [authLoading, isAuthenticated]);

  const fetchTestimonialsData = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Making authenticated API calls to fetch testimonials data...');

      // Fetch section settings first
      const sectionResponse = await authenticatedApiCall<SectionSettings>('/api/admin/home-page/testimonials/', {
        method: 'GET'
      });

      // Try to fetch testimonial items, but handle if endpoint doesn't exist
      let itemsResponse;
      try {
        itemsResponse = await authenticatedApiCall<{ data: TestimonialData[] }>('/api/admin/home-page/testimonials/items/', {
          method: 'GET'
        });
      } catch (error) {
        console.warn('Testimonials items endpoint may not be implemented yet:', error);
        itemsResponse = { success: false, message: 'Endpoint not implemented', data: [] };
      }

      console.log('📥 Section response:', sectionResponse);
      console.log('📥 Items response:', itemsResponse);

      // Handle section settings
      if (sectionResponse.success && sectionResponse.data) {
        setSectionSettings(sectionResponse.data);
        setOriginalSectionSettings(sectionResponse.data);
      } else {
        console.warn('No section settings in response, keeping default values');
        showToast(createToast.warning(
          'Section settings not loaded',
          'Using default section settings. Please check your connection.'
        ));
      }

      // Handle testimonial items
      if (itemsResponse.success && itemsResponse.data) {
        // According to API documentation, data should be an array of testimonials
        const testimonialsArray = itemsResponse.data.data || itemsResponse.data || [];

        setTestimonials(testimonialsArray);
        setOriginalTestimonials(testimonialsArray);
      } else {
        console.warn('No testimonials in response, keeping empty array');
        console.warn('Items response error:', itemsResponse.message, itemsResponse.errors);

        // If it's a validation error, it might mean the endpoint doesn't exist yet
        if (itemsResponse.errors || itemsResponse.message?.includes('validation') || itemsResponse.message?.includes('not found')) {
          showToast(createToast.info(
            'Working in Development Mode',
            'The testimonials API is not ready yet. You can still create and edit testimonials locally for testing.'
          ));

          // Load some sample data for development
          const sampleTestimonials: TestimonialData[] = [
            {
              id: 1,
              name: { en: 'Ahmed Al Mansouri', ar: 'أحمد المنصوري' },
              title: { en: 'Property Investor', ar: 'مستثمر عقاري' },
              content: {
                en: 'My investment with Mazaya Capital has consistently delivered above-market returns. Their professional team provides exceptional support and transparent communication throughout the investment process.',
                ar: 'استثماري مع مزايا كابيتال حقق عوائد فوق السوق باستمرار. فريقهم المحترف يقدم دعماً استثنائياً وتواصلاً شفافاً طوال عملية الاستثمار.'
              },
              initials: { en: 'AAM', ar: 'أ أ م' },
              rating: 5,
              avatar_url: ''
            }
          ];
          setTestimonials(sampleTestimonials);
          setOriginalTestimonials(sampleTestimonials);
        } else {
          showToast(createToast.warning(
            'Testimonials not loaded',
            'No testimonials found. You can add new ones below.'
          ));
        }
      }
    } catch (error) {
      console.error('Error fetching testimonials data:', error);
      showToast(createToast.error(
        'Error loading testimonials data',
        'Please check your connection and try again'
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAll = async () => {
    if (isSaving) return;

    console.log('✅ Starting save process...');
    setIsSaving(true);
    try {
      // Save section settings
      const sectionResponse = await authenticatedApiCall<SectionSettings>('/api/admin/home-page/testimonials/', {
        method: 'PUT',
        body: JSON.stringify(sectionSettings)
      });

      console.log('📥 Section save response:', sectionResponse);

      if (sectionResponse.success) {
        showToast(createToast.success(
          'Section settings updated successfully',
          'Section content has been saved'
        ));

        // Update the original data with the response data
        if (sectionResponse.data) {
          setOriginalSectionSettings(sectionResponse.data);
        }
      } else {
        showToast(createToast.error(
          'Failed to save section settings',
          sectionResponse.message || 'Please try again'
        ));
      }
    } catch (error) {
      console.error('Error saving section settings:', error);
      showToast(createToast.error(
        'Error saving section settings',
        'Please check your connection and try again'
      ));
    } finally {
      setIsSaving(false);
    }
  };

  const addTestimonial = async () => {
    const newTestimonialData = {
      name: { en: '', ar: '' },
      title: { en: '', ar: '' },
      content: { en: '', ar: '' },
      initials: { en: '', ar: ''},
      rating: 5,
    };

    // Create a temporary testimonial for editing
    const tempTestimonial: TestimonialData = {
      id: -1, // Temporary ID for new testimonials
      ...newTestimonialData,
    };

    setTestimonials([...testimonials, tempTestimonial]);
    setEditingTestimonial('-1');
  };

  const deleteTestimonial = async (id: number) => {
    if (id === -1) {
      // Remove temporary testimonial
      setTestimonials(testimonials.filter(t => t.id !== id));
      return;
    }

    try {
      const response = await authenticatedApiCall(`/api/admin/home-page/testimonials/items/${id}/`, {
        method: 'DELETE'
      });

      if (response.success) {
        setTestimonials(testimonials.filter(t => t.id !== id));
        showToast(createToast.success(
          'Testimonial deleted successfully',
          'The testimonial has been removed'
        ));
      } else {
        showToast(createToast.error(
          'Failed to delete testimonial',
          response.message || 'Please try again'
        ));
      }
    } catch (error) {
      console.error('Error deleting testimonial, using local fallback:', error);

      // Local fallback for development
      setTestimonials(testimonials.filter(t => t.id !== id));
      showToast(createToast.success(
        'Testimonial deleted locally',
        'The testimonial has been removed (API not available)'
      ));
    }
  };

  const saveTestimonial = async (testimonialData: TestimonialData, avatarFile?: File) => {
    try {
      // Validate required fields
      if (!testimonialData.name.en || !testimonialData.name.ar) {
        showToast(createToast.error('Validation Error', 'Name is required in both English and Arabic'));
        return;
      }
      if (!testimonialData.title.en || !testimonialData.title.ar) {
        showToast(createToast.error('Validation Error', 'Title is required in both English and Arabic'));
        return;
      }
      if (!testimonialData.content.en || !testimonialData.content.ar) {
        showToast(createToast.error('Validation Error', 'Content is required in both English and Arabic'));
        return;
      }
      if (!testimonialData.initials.en || !testimonialData.initials.ar) {
        showToast(createToast.error('Validation Error', 'Initials are required in both English and Arabic'));
        return;
      }

      const formData = new FormData();

      // Add testimonial data as JSON strings (as shown in API documentation CURL examples)
      formData.append('name', JSON.stringify(testimonialData.name));
      formData.append('title', JSON.stringify(testimonialData.title));
      formData.append('content', JSON.stringify(testimonialData.content));
      formData.append('initials', JSON.stringify(testimonialData.initials));
      formData.append('rating', testimonialData.rating.toString());

      // Add avatar file if provided
      if (avatarFile) {
        formData.append('avatar_image', avatarFile);
      }

      console.log('📤 Sending testimonial data:', {
        name: testimonialData.name,
        title: testimonialData.title,
        content: testimonialData.content,
        initials: testimonialData.initials,
        rating: testimonialData.rating,
        hasAvatarFile: !!avatarFile
      });

      // Log FormData contents for debugging
      console.log('📤 FormData contents:');
      for (let [key, value] of formData.entries()) {
        console.log(`${key}:`, value);
      }

      let response;

      // Try the API call with FormData first
      try {
        if (testimonialData.id === -1) {
          // Create new testimonial
          response = await authenticatedApiCall<TestimonialData>('/api/admin/home-page/testimonials/items/', {
            method: 'POST',
            body: formData,
            headers: {} // Don't set Content-Type for FormData, let browser handle it
          });
        } else {
          // Update existing testimonial
          response = await authenticatedApiCall<TestimonialData>(`/api/admin/home-page/testimonials/items/${testimonialData.id}/`, {
            method: 'PUT',
            body: formData,
            headers: {} // Don't set Content-Type for FormData, let browser handle it
          });
        }
      } catch (error) {
        console.error('FormData approach failed, trying JSON approach:', error);

        // Fallback: Try with JSON format
        const jsonData = {
          name: testimonialData.name,
          title: testimonialData.title,
          content: testimonialData.content,
          initials: testimonialData.initials,
          rating: testimonialData.rating
        };

        try {
          if (testimonialData.id === -1) {
            response = await authenticatedApiCall<TestimonialData>('/api/admin/home-page/testimonials/items/', {
              method: 'POST',
              body: JSON.stringify(jsonData)
            });
          } else {
            response = await authenticatedApiCall<TestimonialData>(`/api/admin/home-page/testimonials/items/${testimonialData.id}/`, {
              method: 'PUT',
              body: JSON.stringify(jsonData)
            });
          }
        } catch (jsonError) {
          console.error('Both FormData and JSON approaches failed. Using local fallback:', jsonError);

          // Local fallback for development
          const newTestimonial: TestimonialData = {
            ...testimonialData,
            id: testimonialData.id === -1 ? Date.now() : testimonialData.id,
            avatar_url: avatarFile ? URL.createObjectURL(avatarFile) : testimonialData.avatar_url
          };

          response = {
            success: true,
            data: newTestimonial,
            message: 'Saved locally (API not available)'
          };
        }
      }

      if (response.success && response.data) {
        if (testimonialData.id === -1) {
          // Replace temporary testimonial with the new one
          setTestimonials(testimonials.map(t => t.id === -1 ? response.data! : t));
        } else {
          // Update existing testimonial
          setTestimonials(testimonials.map(t => t.id === testimonialData.id ? response.data! : t));
        }

        setEditingTestimonial(null);
        showToast(createToast.success(
          testimonialData.id === -1 ? 'Testimonial created successfully' : 'Testimonial updated successfully',
          'Changes have been saved'
        ));
      } else {
        console.error('❌ Save testimonial failed:', response);

        // Handle validation errors specifically
        if (response.errors && typeof response.errors === 'object') {
          const errorMessages = Object.entries(response.errors)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');

          showToast(createToast.error(
            'Validation Error',
            `Please fix the following issues:\n${errorMessages}`
          ));
        } else {
          showToast(createToast.error(
            'Failed to save testimonial',
            response.message || 'Please try again'
          ));
        }
      }
    } catch (error) {
      console.error('Error saving testimonial:', error);
      showToast(createToast.error(
        'Error saving testimonial',
        'Please check your connection and try again'
      ));
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading testimonials data...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Testimonials Section</h1>
          <p className="text-gray-400 mt-1">Manage the testimonials section content, design, and background effects</p>
        </div>
        <button
          onClick={handleSaveAll}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <FiSave className="mr-2 h-4 w-4" />
              Save All Changes
            </>
          )}
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Settings */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiSettings className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Settings
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'settings' ? null : 'settings')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'settings' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'settings' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'settings' ? (
            <div className="space-y-6">
              {/* Content Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Content Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Badge Text (English)</label>
                    <input
                      type="text"
                      value={sectionSettings?.badge?.en || ""}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        badge: { ...prev.badge, en: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Badge Text (Arabic)</label>
                    <input
                      type="text"
                      value={sectionSettings?.badge?.ar || ""}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        badge: { ...prev.badge, ar: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                    <input
                      type="text"
                      value={sectionSettings?.title?.en || ""}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        title: { ...prev.title, en: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                    <input
                      type="text"
                      value={sectionSettings?.title?.ar || ""}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        title: { ...prev.title, ar: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Subtitle (English)</label>
                    <textarea
                      value={sectionSettings?.subtitle?.en || ""}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        subtitle: { ...prev.subtitle, en: e.target.value }
                      }))}
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Subtitle (Arabic)</label>
                    <textarea
                      value={sectionSettings?.subtitle?.ar || ""}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        subtitle: { ...prev.subtitle, ar: e.target.value }
                      }))}
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>



              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
                  <p className="text-white bg-gray-700 p-3 rounded">{sectionSettings?.badge?.en || "Client Success Stories"}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
                  <p className="text-white bg-gray-700 p-3 rounded" dir="rtl">{sectionSettings?.badge?.ar || "قصص نجاح العملاء"}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                  <p className="text-white bg-gray-700 p-3 rounded">{sectionSettings?.title?.en || "Client Testimonials"}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                  <p className="text-white bg-gray-700 p-3 rounded" dir="rtl">{sectionSettings?.title?.ar || "شهادات العملاء"}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                  <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm">{sectionSettings?.subtitle?.en || "Hear what our satisfied clients say about their experience with Mazaya Capital"}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                  <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm" dir="rtl">{sectionSettings?.subtitle?.ar || "اسمع ما يقوله عملاؤنا الراضون عن تجربتهم مع مزايا كابيتال"}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Testimonials Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiUsers className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Testimonials Management
            </h2>
            <button
              onClick={addTestimonial}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Testimonial
            </button>
          </div>

          <div className="space-y-6">
            {testimonials.map((testimonial, index) => (
              <div key={testimonial.id} className="bg-gray-700 rounded-lg border border-gray-600 p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    <FiMessageSquare className="mr-2 h-4 w-4 text-[#00C2FF]" />
                    Testimonial #{index + 1}
                  </h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingTestimonial(editingTestimonial === testimonial.id.toString() ? null : testimonial.id.toString())}
                      className="inline-flex items-center px-3 py-1 text-sm bg-gray-600 text-gray-300 rounded hover:bg-gray-500"
                    >
                      {editingTestimonial === testimonial.id.toString() ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
                      {editingTestimonial === testimonial.id.toString() ? 'Cancel' : 'Edit'}
                    </button>
                    <button
                      onClick={() => deleteTestimonial(testimonial.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-500"
                    >
                      <FiTrash2 className="mr-1 h-4 w-4" />
                      Delete
                    </button>
                  </div>
                </div>

                {editingTestimonial === testimonial.id.toString() ? (
                  <TestimonialForm
                    testimonial={testimonial}
                    onSave={(updatedTestimonial, avatarFile) => saveTestimonial(updatedTestimonial, avatarFile)}
                    onCancel={() => setEditingTestimonial(null)}
                  />
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Name (English)</label>
                        <p className="text-white">{testimonial.name.en}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Name (Arabic)</label>
                        <p className="text-white" dir="rtl">{testimonial.name.ar}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Title (English)</label>
                        <p className="text-gray-300">{testimonial.title.en}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Title (Arabic)</label>
                        <p className="text-gray-300" dir="rtl">{testimonial.title.ar}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Content (English)</label>
                        <p className="text-gray-300 text-sm">{testimonial.content.en.substring(0, 100)}...</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Content (Arabic)</label>
                        <p className="text-gray-300 text-sm" dir="rtl">{testimonial.content.ar.substring(0, 100)}...</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Initials (English)</label>
                        <p className="text-gray-300 text-sm">{testimonial.initials.en.substring(0, 100)}...</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Initials (Arabic)</label>
                        <p className="text-gray-300 text-sm" dir="rtl">{testimonial.initials.ar.substring(0, 100)}...</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Rating</label>
                        <div className="flex space-x-1">
                          {[...Array(5)].map((_, i) => (
                            <FiStar key={i} className={`h-4 w-4 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-500'}`} />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Section Header Preview */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Section Header Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-600">
            <div className="text-center space-y-8">
              {/* English Version */}
              <div>
                <div className="inline-block px-4 py-1 rounded-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm text-indigo-300 text-sm font-medium mb-4">
                  {sectionSettings?.badge?.en || "Client Success Stories"}
                </div>
                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
                  <span className="relative inline-block">
                    {sectionSettings?.title?.en || "Client Testimonials"}
                    <span className="absolute -bottom-2 start-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></span>
                  </span>
                </h2>
                <p className="text-indigo-200 max-w-2xl mx-auto">
                  {sectionSettings?.subtitle?.en || "Hear what our satisfied clients say about their experience with Mazaya Capital"}
                </p>
              </div>

              {/* Arabic Version */}
              <div dir="rtl">
                <div className="inline-block px-4 py-1 rounded-full bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-sm text-blue-300 text-sm font-medium mb-4">
                  {sectionSettings?.badge?.ar || "قصص نجاح العملاء"}
                </div>
                <h2 className="text-2xl md:text-3xl font-bold mb-4 text-blue-100">
                  <span className="relative inline-block">
                    {sectionSettings?.title?.ar || "شهادات العملاء"}
                    <span className="absolute -bottom-2 start-0 w-full h-1 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500"></span>
                  </span>
                </h2>
                <p className="text-blue-200 max-w-2xl mx-auto">
                  {sectionSettings?.subtitle?.ar || "اسمع ما يقوله عملاؤنا الراضون عن تجربتهم مع مزايا كابيتال"}
                </p>
              </div>
            </div>

            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
              {testimonials && testimonials.length > 0 ? testimonials.slice(0, 3).map((testimonial) => (
                <div key={testimonial.id} className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                  <div className="flex items-center mb-3">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white font-bold mr-3">
                      {testimonial.avatar_url ? (
                        <img src={testimonial.avatar_url} alt={testimonial.name.en} className="w-full h-full rounded-full object-cover" />
                      ) : (
                        testimonial.initials.en
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="space-y-1">
                        <h4 className="text-white font-medium">{testimonial.name.en}</h4>
                        <h4 className="text-blue-300 font-medium text-sm" dir="rtl">{testimonial.name.ar}</h4>
                      </div>
                      <div className="space-y-1">
                        <p className="text-gray-400 text-sm">{testimonial.title.en}</p>
                        <p className="text-gray-500 text-xs" dir="rtl">{testimonial.title.ar}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex mb-3">
                    {[...Array(5)].map((_, i) => (
                      <FiStar key={i} className={`h-3 w-3 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-500'}`} />
                    ))}
                  </div>
                  <div className="space-y-2">
                    <p className="text-gray-300 text-sm leading-relaxed">{testimonial.content.en.substring(0, 80)}...</p>
                    <p className="text-blue-200 text-sm leading-relaxed" dir="rtl">{testimonial.content.ar.substring(0, 80)}...</p>
                  </div>
                </div>
              )) : (
                <div className="col-span-3 text-center py-8">
                  <p className="text-gray-400">No testimonials available</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Live Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview (Full Component)
          </h2>
          <div className="bg-gray-900 rounded-lg border border-gray-600 overflow-hidden">
            <div className="transform scale-75 origin-top-left w-[133.33%] h-auto">
              <TestimonialsPreview
                sectionSettings={sectionSettings}
                testimonials={testimonials}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Testimonial Form Component
function TestimonialForm({
  testimonial,
  onSave,
  onCancel
}: {
  testimonial: TestimonialData;
  onSave: (testimonial: TestimonialData, avatarFile?: File) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(testimonial);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(testimonial.avatar_url || '');
  const [uploadMethod, setUploadMethod] = useState<'upload' | 'url'>('upload');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);

      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
    }
  };

  const handleImageClick = () => {
    document.getElementById(`avatar-upload-${formData.id}`)?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData, selectedFile || undefined);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Name (English)</label>
          <input
            type="text"
            value={formData.name.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              name: { ...prev.name, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Name (Arabic)</label>
          <input
            type="text"
            value={formData.name.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              name: { ...prev.name, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Content (English)</label>
          <textarea
            value={formData.content.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              content: { ...prev.content, en: e.target.value }
            }))}
            rows={4}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Content (Arabic)</label>
          <textarea
            value={formData.content.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              content: { ...prev.content, ar: e.target.value }
            }))}
            rows={4}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Initials (English)</label>
          <input
            type='text'
            value={formData.initials.en}
            onChange={(e) => setFormData(prev => ({ ...prev, initials: { ...prev.initials, en: e.target.value.toUpperCase() } }))}
            maxLength={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Initials (Arabic)</label>
          <input
            type='text'
            value={formData.initials.ar}
            onChange={(e) => setFormData(prev => ({ ...prev, initials: { ...prev.initials, ar: e.target.value.toUpperCase() } }))}
            maxLength={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Rating</label>
          <select
            value={formData.rating}
            onChange={(e) => setFormData(prev => ({ ...prev, rating: parseInt(e.target.value) }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          >
            {[1, 2, 3, 4, 5].map(rating => (
              <option key={rating} value={rating}>{rating} Star{rating > 1 ? 's' : ''}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Avatar Image</label>

          {/* Hidden File Input */}
          <input
            id={`avatar-upload-${formData.id}`}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />

          {/* Upload Method Toggle */}
          <div className="flex space-x-2 mb-3">
            <button
              type="button"
              onClick={() => setUploadMethod('upload')}
              className={`px-3 py-1 text-xs rounded transition-colors ${
                uploadMethod === 'upload'
                  ? 'bg-[#00C2FF] text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Upload Image
            </button>
            <button
              type="button"
              onClick={() => setUploadMethod('url')}
              className={`px-3 py-1 text-xs rounded transition-colors ${
                uploadMethod === 'url'
                  ? 'bg-[#00C2FF] text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Image URL
            </button>
          </div>

          {uploadMethod === 'upload' ? (
            // Image Upload Area
            <div
              onClick={handleImageClick}
              className="w-full h-20 bg-gray-700 rounded-md overflow-hidden cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group relative"
            >
              {previewUrl ? (
                <div className="relative w-full h-full">
                  <img
                    src={previewUrl}
                    alt="Avatar Preview"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                    <div className="text-center text-white opacity-0 group-hover:opacity-100">
                      <FiImage className="h-4 w-4 mx-auto mb-1" />
                      <p className="text-xs">Click to change</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
                  <div className="text-center">
                    <FiImage className="h-6 w-6 mx-auto mb-1" />
                    <p className="text-xs font-medium">Upload avatar</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            // URL Input
            <input
              type="url"
              value={previewUrl}
              onChange={(e) => {
                setPreviewUrl(e.target.value);
              }}
              placeholder="https://example.com/avatar.jpg"
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          )}

          {/* Upload Info */}
          {selectedFile && uploadMethod === 'upload' && (
            <div className="mt-2 p-2 bg-gray-700 rounded border border-gray-600">
              <div className="flex items-center text-green-400 text-xs">
                <FiImage className="mr-1 h-3 w-3" />
                <span>Selected: {selectedFile.name}</span>
              </div>
            </div>
          )}

          {/* Image Preview for URL */}
          {uploadMethod === 'url' && previewUrl && (
            <div className="mt-2">
              <img
                src={previewUrl}
                alt="Avatar Preview"
                className="w-12 h-12 object-cover rounded-full border border-gray-600"
                onError={(e) => {
                  e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMzc0MTUxIi8+Cjx0ZXh0IHg9IjI0IiB5PSIyOCIgZmlsbD0iIzZCNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIj5ObyBJbWFnZTwvdGV4dD4KPHN2Zz4K';
                }}
              />
            </div>
          )}
        </div>
      </div>



      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Testimonial
        </button>
      </div>
    </form>
  );
}

// TestimonialsPreview component that uses the actual Testimonials component with custom data
function TestimonialsPreview({
  sectionSettings,
  testimonials
}: {
  sectionSettings: SectionSettings;
  testimonials: TestimonialData[];
}) {
  // Transform the admin data to match the Testimonials component format
  const transformedTestimonials = testimonials.map(testimonial => ({
    id: testimonial.id,
    name: testimonial.name.en,
    position: testimonial.title.en,
    quote: testimonial.content.en,
    image: testimonial.avatar_url || "/images/testimonial-placeholder.jpg",
    rating: testimonial.rating,
  }));

  return (
    <div className="relative">
      {/* Override the Testimonials component with our custom data */}
      <div className="min-h-screen flex items-center py-24 overflow-hidden bg-[#0A1429] relative">
        {/* Background elements from the original component */}
        <div className="absolute inset-0 w-full h-full overflow-hidden -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-[#0a0e29] via-[#0b1233] to-[#121f52] z-[-5]"></div>
          <div className="absolute inset-0 opacity-70 z-[-4]">
            <div className="absolute top-0 -inline-start-1/4 w-[600px] h-[600px] bg-gradient-to-r from-indigo-900/60 to-transparent rounded-full filter blur-[120px] animate-pulse-slow"></div>
            <div className="absolute bottom-0 -inline-end-1/4 w-[600px] h-[600px] bg-gradient-to-l from-purple-900/60 to-transparent rounded-full filter blur-[120px] animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
          </div>
        </div>

        <div className="container mx-auto px-4 relative z-20">
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-1 rounded-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm text-indigo-300 text-sm font-medium mb-4">
              {sectionSettings?.badge?.en || "Client Success Stories"}
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-white">
              <span className="relative inline-block">
                {sectionSettings?.title?.en || "Client Testimonials"}
                <span className="absolute -bottom-2 start-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></span>
              </span>
            </h2>
            <p className="text-indigo-200 max-w-3xl mx-auto text-lg mt-6">
              {sectionSettings?.subtitle?.en || "Hear what our satisfied clients say about their experience with Mazaya Capital"}
            </p>
          </div>

          <div className="max-w-6xl mx-auto relative">
            {/* Show first testimonial */}
            {transformedTestimonials.length > 0 && (
              <div className="relative">
                <div className="min-w-full px-5">
                  <div className="relative backdrop-blur-2xl rounded-2xl overflow-hidden shadow-[0_8px_30px_rgb(0,0,0,0.6)] border border-indigo-500/40">
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/80 via-[#0A1429]/90 to-purple-900/70"></div>

                    <div className="relative p-8 md:p-12">
                      <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12">
                        <div className="w-full md:w-1/3 flex flex-col items-center relative">
                          <div className="absolute w-36 h-36 rounded-full bg-gradient-to-br from-indigo-500/10 to-purple-500/10 blur-md"></div>

                          <div className="w-28 h-28 rounded-full bg-gradient-to-br from-indigo-400 to-purple-600 p-1 mb-6 shadow-lg shadow-indigo-500/20 relative z-10">
                            <div className="w-full h-full rounded-full bg-[#0a1029] flex items-center justify-center text-2xl font-bold text-white">
                              {transformedTestimonials[0].name.split(" ").map(n => n[0]).join("")}
                            </div>
                          </div>

                          <h4 className="font-bold text-2xl text-white text-center mb-2 relative z-10">{transformedTestimonials[0].name}</h4>
                          <p className="text-indigo-300 text-center mb-4 text-lg relative z-10">{transformedTestimonials[0].position}</p>

                          <div className="flex flex-col items-center space-y-3 relative z-10">
                            <div className="flex space-x-1">
                              {[...Array(transformedTestimonials[0].rating)].map((_, i) => (
                                <svg key={i} className="w-6 h-6" viewBox="0 0 20 20">
                                  <defs>
                                    <linearGradient id={`star-gradient-preview-${i}`} x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" stopColor="#FFD700" />
                                      <stop offset="100%" stopColor="#FFA500" />
                                    </linearGradient>
                                  </defs>
                                  <path
                                    fill={`url(#star-gradient-preview-${i})`}
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                                  />
                                </svg>
                              ))}
                            </div>
                            <div className="w-24 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
                          </div>
                        </div>

                        <div className="w-full md:w-2/3 flex flex-col">
                          <div className="relative mb-6">
                            <svg className="h-16 w-16 text-indigo-400/20 absolute -top-8 -inline-start-6" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M9.58,10.58C9.86,11.13 9.46,11.8 8.83,11.8H5.91C5.5,11.8 5.16,11.46 5.16,11.05V8.95C5.16,7.05 6.71,5.5 8.61,5.5C9.73,5.5 10,4.69 10,4.69C10,4.69 10.58,3.61 9.79,3.05C9,2.5 8.12,2.5 8.12,2.5C5.38,2.5 3.16,4.72 3.16,7.45V11.05C3.16,12.54 4.38,13.8 5.91,13.8H8.83C10.11,13.8 10.94,12.11 9.58,11.33C9.58,11.3 9.58,10.58 9.58,10.58M20.84,10.58C21.12,11.13 20.73,11.8 20.11,11.8H17.19C16.77,11.8 16.43,11.46 16.43,11.05V8.95C16.43,7.05 18,5.5 19.89,5.5C21,5.5 21.27,4.69 21.27,4.69C21.27,4.69 21.85,3.61 21.06,3.05C20.27,2.5 19.39,2.5 19.39,2.5C16.66,2.5 14.43,4.72 14.43,7.45V11.05C14.43,12.54 15.66,13.8 17.19,13.8H20.11C21.39,13.8 22.22,12.11 20.84,11.33C20.84,11.3 20.84,10.58 20.84,10.58" />
                            </svg>
                          </div>

                          <p className="text-white text-xl md:text-2xl font-light italic leading-relaxed mb-8">
                            {transformedTestimonials[0].quote}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="absolute top-0 inline-end-0 w-32 h-32 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 blur-2xl rounded-full"></div>
                    <div className="absolute bottom-0 inline-start-0 w-40 h-40 bg-gradient-to-tr from-indigo-500/10 to-purple-500/10 blur-2xl rounded-full"></div>
                    <div className="absolute top-0 inline-end-10 w-1 h-12 bg-gradient-to-b from-indigo-500/60 to-transparent"></div>
                    <div className="absolute bottom-0 inline-start-1/3 w-1 h-16 bg-gradient-to-b from-transparent to-purple-500/60"></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
